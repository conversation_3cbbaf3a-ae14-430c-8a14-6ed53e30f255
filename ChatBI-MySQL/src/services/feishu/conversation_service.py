"""
飞书对话服务模块
负责管理对话历史记录、所有权验证等对话相关功能
"""
import json
from datetime import datetime
from src.utils.logger import logger
from services.chatbot.history_service import (
    save_user_message,
    save_assistant_message,
    get_conversation_history_as_input_list,
    check_conversation_ownership,
)
from src.services.feishu.message_apis import reply_simple_text_message


class ConversationService:
    """对话服务类"""
    
    @staticmethod
    async def validate_conversation_ownership(
        user_name: str, user_email: str, message_id: str, conversation_id: str
    ) -> tuple[bool, bool]:
        """验证对话所有权
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            message_id: 消息ID
            conversation_id: 对话ID
            
        Returns:
            bool: 如果用户有权限访问对话返回True，否则返回False
            bool: 如果对话存在返回True，否则返回False
        """
        is_exists = False
        is_owner = False
        if message_id != conversation_id:
            # 检查对话所有权
            is_owner, is_exists = check_conversation_ownership(user_name, user_email, conversation_id)
            if not is_owner and is_exists:
                logger.warning(
                    f"用户 {user_name} ({user_email}) 尝试回复不属于他的对话 {conversation_id}"
                )
                reply_simple_text_message(
                    message_id,
                    "抱歉，您不能回复其他人的对话。请开始一个新的对话或回复您自己的对话。",
                )
        return is_owner, is_exists
    
    @staticmethod
    async def save_user_message_to_history(
        user_name: str, user_email: str, conversation_id: str, user_query: str
    ):
        """保存用户消息到历史记录
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            conversation_id: 对话ID
            user_query: 用户查询内容
        """
        save_user_message(
            username=user_name,
            email=user_email,
            conversation_id=conversation_id,
            content=user_query,
            timestamp=int(datetime.now().timestamp() * 1000),
        )
        logger.info(f"用户消息已保存到对话 {conversation_id}")
    
    @staticmethod
    async def get_conversation_history(
        user_name: str, user_email: str, conversation_id: str, root_id: str
    ) -> list:
        """获取对话历史记录
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            conversation_id: 对话ID
            root_id: 根消息ID
            
        Returns:
            list: 格式化后的历史消息列表
        """
        history = []
        if root_id:
            try:
                # 使用新的 service 函数获取格式化后的历史记录
                history = get_conversation_history_as_input_list(
                    user_name, user_email, conversation_id
                )
                logger.info(
                    f"通过 history_service 获取到 {len(history)} 条格式化后的历史消息。"
                )
            except Exception as e:
                logger.error(
                    f"通过 history_service 获取历史对话记录失败: {e}", exc_info=True
                )
                # 如果获取历史失败，继续处理但不使用历史记录
                history = []
        else:
            logger.info("No root_id provided, starting with empty history.")
            history = []
        
        return history
    
    @staticmethod
    async def save_assistant_response_to_history(
        user_name: str,
        user_email: str,
        conversation_id: str,
        full_response: str,
        full_log_message: str,
        structured_assistant_message: dict = None,
        used_agents: list = None,
        bot_instance = None  # 新增：bot实例，用于获取Tool Agent日志
    ):
        """保存助手回复到历史记录
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            conversation_id: 对话ID
            full_response: 完整的响应内容
            full_log_message: 完整的日志消息
            structured_assistant_message: 结构化的助手消息
        """
        output_as_input_json = None
        
        # 尝试序列化结构化消息用于 output_as_input 字段
        if structured_assistant_message and isinstance(structured_assistant_message, dict):
            try:
                output_as_input_json = json.dumps(
                    structured_assistant_message, ensure_ascii=False, indent=2
                )
            except Exception as json_err:
                logger.error(
                    f"无法将 structured_assistant_message 序列化为 JSON: {json_err}"
                )
                # 序列化失败，output_as_input_json 保持 None
        
        # 处理agent信息
        agent_str = None
        if used_agents and len(used_agents) > 0:
            agent_str = ",".join(used_agents)
            logger.info(f"保存助手消息，使用的agent: {agent_str}")

        # 获取Tool Agent的详细执行日志
        enhanced_logs = full_log_message
        try:
            if bot_instance and hasattr(bot_instance, 'get_aggregated_tool_logs'):
                # 获取聚合的Tool Agent日志
                tool_agent_logs = bot_instance.get_aggregated_tool_logs()
                if tool_agent_logs and tool_agent_logs.strip():
                    # 将Tool Agent日志添加到现有日志中
                    if enhanced_logs:
                        enhanced_logs += "\n\n" + tool_agent_logs
                    else:
                        enhanced_logs = tool_agent_logs
                    logger.debug(f"已添加Tool Agent详细日志到飞书消息，总长度: {len(enhanced_logs)}")
            else:
                logger.debug("bot_instance不可用或没有get_aggregated_tool_logs方法，跳过Tool Agent日志收集")
        except Exception as e:
            logger.warning(f"获取Tool Agent日志时出错: {e}")

        # 调用 save_assistant_message，分别传递 stream logs 和 structured output JSON
        save_assistant_message(
            username=user_name,
            email=user_email,
            conversation_id=conversation_id,
            content=full_response,  # content 始终是流式文本响应
            timestamp=int(datetime.now().timestamp() * 1000),
            logs=enhanced_logs,  # 传递增强后的日志（包含Tool Agent日志）
            output_as_input=output_as_input_json,  # 传递序列化的结构化消息到 output_as_input 字段
            agent=agent_str  # 传递agent信息化后的结构化输出（或 None）到新字段
        )
        # 更新日志消息以反映保存状态
        logger.info(
            f"助手回复已保存到对话 {conversation_id} (结构化输出状态: {'已保存' if output_as_input_json else '未保存或失败'})"
        )
