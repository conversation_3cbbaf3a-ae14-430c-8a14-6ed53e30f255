"""
协调者Bot实现 - Agent as Tool架构

这个Bot作为主协调者，负责：
1. 接收用户查询并保持对话主体地位
2. 分析查询内容，决定调用哪些专业Agent工具
3. 协调多个专业Agent的执行（支持并行）
4. 聚合和整理专业Agent的结果
5. 统一错误处理和重试机制
"""
from typing import Optional, Dict, Any, List
from datetime import datetime

from agents import Agent, Model
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.services.agent.utils.agent_tools import AgentToolRegistry, create_parallel_analysis_tool
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.logger import logger


class CoordinatorBot(DataFetcherBot):
    """
    协调者Bot - Agent as Tool架构的核心

    负责协调多个专业Agent工具，实现统一的用户交互界面。
    继承DataFetcherBot以使用配置文件驱动的架构：
    - 使用YAML配置文件定义工具和模型设置
    - 支持商品搜索工具
    - 动态添加专业Agent工具
    - 支持并行调用多个Agent
    """

    def __init__(self, user_info: Dict[str, Any], config_file: str = "coordinator_bot.yml"):
        # 调用父类构造函数，加载配置文件
        super().__init__(user_info, config_file)
        # 初始化Agent工具注册中心
        self.tool_registry = AgentToolRegistry(user_info)
        # 存储额外的专业Agent工具
        self.agent_tools = []
        self._initialize_agent_tools()

    def _initialize_agent_tools(self):
        """初始化专业Agent工具"""
        try:
            # 注册数据获取Agent工具
            data_fetcher_tools = self.tool_registry.register_data_fetcher_tools()
            self.agent_tools.extend(data_fetcher_tools)

            # 添加并行分析工具
            parallel_tool = create_parallel_analysis_tool(self.tool_registry)
            self.agent_tools.append(parallel_tool)

            logger.info(f"🔧 协调者Bot初始化专业Agent工具完成，共注册{len(self.agent_tools)}个专业工具")

        except Exception as e:
            logger.exception(f"初始化专业Agent工具失败: {e}")
            self.agent_tools = []

    def get_description(self) -> str:
        return "我是智能协调助手，能够分析您的问题并调用最合适的专业分析工具来为您提供准确的答案。我可以处理销售分析、仓储物流、知识查询等多个领域的问题，并能搜索商品信息与用户确认。"

    def get_available_tools_description(self) -> str:
        """获取可用工具的描述信息"""
        if not self.tool_registry.registered_tools:
            return "暂无可用的专业分析工具"
        
        descriptions = []
        for tool_name, tool_func in self.tool_registry.registered_tools.items():
            # 尝试获取工具的描述信息
            desc = getattr(tool_func, 'description', f"{tool_name}专业分析工具")
            descriptions.append(f"- {tool_name}: {desc}")
        
        return "\n".join(descriptions)

    def create_agent(self, model: Optional[Model] = None, model_settings_str: Optional[str] = None) -> Agent:
        """
        创建协调者Agent实例，重写父类方法以添加专业Agent工具

        Args:
            model: 使用的模型实例
            model_settings_str: 模型设置字符串

        Returns:
            Agent: 配置好的协调者Agent
        """
        # 调用父类方法获取基础Agent配置
        agent_name = self.config.get("agent_name", "coordinator_bot")
        agent_description = self.config.get("agent_description", "")
        tools = self.config.get("tools", [])

        # 从配置文件读取model配置
        config_model = self.config.get("model")
        config_model_settings = self.config.get("model_settings")

        logger.info(f"协调者Bot配置: agent_name={agent_name}, tools={tools}, config_model={config_model}")

        # 获取配置文件中定义的工具（如商品搜索工具）
        config_tool_list = tool_manager.get_tool_list([tool['name'] for tool in tools])

        # 合并配置工具和专业Agent工具
        all_tools = config_tool_list + self.agent_tools

        # 使用配置文件中的agent_description作为指令
        instruction = agent_description

        # 添加用户实时指令
        realtime_instruction = self.get_user_realtime_instruction()
        full_instruction = f"{instruction}\n\n## 用户上下文信息\n{realtime_instruction}"

        # 确定使用的模型
        final_model = self._safe_get_model(config_model, model)

        # 处理model_settings
        model_settings = self._safe_get_model_settings(config_model_settings, model_settings_str)

        logger.info(f"协调者Bot使用模型: {final_model}")
        logger.info(f"协调者Bot工具数量: 配置工具={len(config_tool_list)}, 专业Agent工具={len(self.agent_tools)}, 总计={len(all_tools)}")

        # 创建Agent实例
        agent_kwargs = {
            "name": f"{agent_name}_coordinator",
            "instructions": full_instruction,
            "model": final_model,
            "tools": all_tools
        }

        if model_settings:
            agent_kwargs["model_settings"] = model_settings

        return Agent(**agent_kwargs)

    def get_tool_usage_stats(self) -> Dict[str, Any]:
        """获取工具使用统计信息"""
        config_tools = self.config.get("tools", [])
        return {
            "config_tools": len(config_tools),
            "agent_tools": len(self.agent_tools),
            "total_tools": len(config_tools) + len(self.agent_tools),
            "registered_agents": list(self.tool_registry.registered_tools.keys()),
            "has_parallel_tool": any(
                getattr(tool, '__name__', '').startswith('parallel_analysis')
                for tool in self.agent_tools
            )
        }

    def validate_tools(self) -> List[str]:
        """验证工具配置，返回问题列表"""
        issues = []

        # 检查配置文件中的工具
        config_tools = self.config.get("tools", [])
        if not config_tools:
            issues.append("配置文件中没有定义工具")

        # 检查专业Agent工具
        if not self.agent_tools:
            issues.append("没有可用的专业Agent工具")

        if not self.tool_registry.registered_tools:
            issues.append("没有注册任何Agent工具")

        # 检查工具函数的有效性
        for tool_name, tool_func in self.tool_registry.registered_tools.items():
            # FunctionTool对象也是有效的工具
            if not (callable(tool_func) or hasattr(tool_func, '__call__')):
                issues.append(f"工具 {tool_name} 不是可调用对象")

        return issues

    def get_user_realtime_instruction(self) -> str:
        """
        重写父类方法，为协调者Agent定制实时指令
        """
        # 获取基础的用户信息，但不包含SQL相关的指令
        from datetime import datetime
        import textwrap
        from src.services.agent.utils.permissions import get_user_permission

        try:
            date_time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            data_permission_markup = get_user_permission(self.user_info)

            # 为协调者定制的实时指令
            coordinator_instruction = textwrap.dedent(
                f"""
                ## 鲜沐ChatBI - AI助手实时指令
                当前时间: {date_time_of_now}
                用户: {self.user_name} ({self.job_title})

                ### 核心职责
                - 你是智能AI助手，负责分析用户问题并调用合适的专业工具
                - 当问题涉及多个领域时，可以使用并行分析工具同时调用多个专业Agent
                - 当用户提到商品名称但不够具体时，使用商品搜索工具确认具体SKU
                - 始终要调用工具来获取数据，不要凭空回答问题
                - 要对工具返回的结果进行整理和总结，提供清晰易懂的答案

                ### 飞书链接处理规则
                - 当专家Agent返回的结果中包含飞书多维表格链接时，必须单独一行醒目显示
                - 识别包含"上传至飞书文档"或"飞书多维表格"等关键词的消息
                - 提取其中的链接并用以下格式显示：
                  📊 **完整数据已上传至飞书多维表格，请点击查看：[文档名称](链接地址)**

                ### 权限控制
                数据权限申明：{data_permission_markup}

                ### 重要提醒
                - 始终用中文回答用户问题
                - 专业Agent工具只需要传递用户的具体查询问题，不需要传递完整对话历史
                - 如果工具执行失败，要向用户说明情况并建议替代方案
                - **特别重要**：当工具返回结果包含飞书链接时，必须醒目显示给用户，不能遗漏
                """
            ).strip()

            return coordinator_instruction
        except Exception as e:
            logger.exception(f"生成协调者实时指令时发生错误: {e}")
            return f"生成实时指令时发生错误: {str(e)}"

    def get_aggregated_tool_logs(self) -> str:
        """
        获取聚合的Tool Agent执行日志，格式化为适合存储到chat_history的字符串

        Returns:
            str: 格式化的日志字符串
        """
        try:
            # 从工具注册中心获取详细的执行日志
            detailed_logs = self.tool_registry.get_all_execution_logs()

            # 获取执行统计
            execution_summary = self.tool_registry.get_execution_summary()

            log_lines = []

            # 添加执行摘要
            log_lines.append("=== Agent as Tool 执行日志 ===")
            log_lines.append(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            log_lines.append(f"总执行次数: {execution_summary.get('total', 0)}")
            log_lines.append(f"成功执行: {execution_summary.get('successful', 0)}")
            log_lines.append(f"失败执行: {execution_summary.get('failed', 0)}")
            log_lines.append(f"总执行时间: {execution_summary.get('total_execution_time', 0):.2f}秒")
            log_lines.append(f"平均执行时间: {execution_summary.get('avg_execution_time', 0):.2f}秒")

            # 添加使用的Agent列表
            agents = execution_summary.get('agents', [])
            if agents:
                log_lines.append(f"使用的专业Agent: {', '.join(agents)}")

            # 添加详细的执行日志
            if detailed_logs and detailed_logs != "无Tool Agent执行记录":
                log_lines.append("\n" + detailed_logs)

            log_lines.append("\n=== 执行日志结束 ===")

            return "\n".join(log_lines)

        except Exception as e:
            logger.exception(f"获取聚合Tool Agent日志时出错: {e}")
            return f"获取Tool Agent日志时出错: {str(e)}"

    def get_detailed_tool_execution_logs(self) -> List[Dict[str, Any]]:
        """
        获取详细的Tool Agent执行日志，包含每个Agent的具体执行信息

        Returns:
            List[Dict[str, Any]]: 详细的执行日志列表
        """
        try:
            detailed_logs = []

            # 从工具注册中心获取详细信息
            # 注意：这里需要扩展AgentToolRegistry来保存详细的执行结果
            # 目前先返回基础信息，后续可以进一步完善

            used_agents = self.tool_registry.executor.get_used_agents()

            for agent_name in used_agents:
                agent_log = {
                    "agent_name": agent_name,
                    "execution_time": datetime.now().isoformat(),
                    "status": "completed",
                    "details": f"Agent {agent_name} 执行完成"
                }
                detailed_logs.append(agent_log)

            return detailed_logs

        except Exception as e:
            logger.exception(f"获取详细Tool Agent执行日志时出错: {e}")
            return [{"error": f"获取详细日志时出错: {str(e)}"}]

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要信息"""
        config_tools = self.config.get("tools", [])
        return {
            "coordinator_info": {
                "user_name": self.user_name,
                "job_title": self.job_title,
                "config_file": self.config_file,
                "config_tools": len(config_tools),
                "agent_tools": len(self.agent_tools),
                "total_tools": len(config_tools) + len(self.agent_tools),
                "registered_agents": list(self.tool_registry.registered_tools.keys())
            },
            "executor_stats": self.tool_registry.executor.get_execution_stats(),
            "tool_validation": self.validate_tools(),
            "tool_usage_stats": self.get_tool_usage_stats(),
            "aggregated_logs": self.get_aggregated_tool_logs(),
            "detailed_logs": self.get_detailed_tool_execution_logs()
        }
