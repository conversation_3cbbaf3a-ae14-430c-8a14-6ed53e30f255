"""
优化后的查询运行器 - 解决并发问题
"""
import asyncio
import json
import queue
import threading
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Generator
import weakref

from agents import Runner, RunConfig, ModelSettings
from src.models.user_info_class import UserInfo
from src.services.agent.bots.coordinator_bot import CoordinatorBot
from src.services.agent.utils.formatter import format_event_message
from src.services.agent.utils.model_provider import LITE_LLM_MODEL
from src.services.chatbot.history_service import (
    save_assistant_message,
    get_conversation_history_as_input_list
)
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.system_monitor import start_system_monitoring, stop_system_monitoring, log_current_usage
from src.utils.image_utils import process_images_for_ai

# 全局线程池 - 避免频繁创建销毁线程
THREAD_POOL = concurrent.futures.ThreadPoolExecutor(
    max_workers=20,  # 可根据需要调整
    thread_name_prefix="agent_worker"
)

# 移除事件循环池，改为按需创建和销毁事件循环以避免资源泄漏

# 改进的后台任务队列
class BackgroundTaskManager:
    def __init__(self, max_workers=10):
        self.task_queue = queue.Queue()
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="background_worker"
        )
        self.active_tasks = weakref.WeakSet()
        self._start_workers()

    def _start_workers(self):
        """启动后台工作线程"""
        for _ in range(3):  # 启动3个工作线程
            future = self.executor.submit(self._worker)
            self.active_tasks.add(future)

    def _worker(self):
        """后台工作线程"""
        while True:
            try:
                task = self.task_queue.get(timeout=30)  # 30秒超时
                if task is None:
                    break

                # 处理任务
                self._process_task(task)

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Background worker error: {e}")
            finally:
                self.task_queue.task_done()

    def _process_task(self, task):
        """处理后台任务"""
        try:
            user_query = task["user_query"]
            user_info = task["user_info"]
            access_token = task["access_token"]
            conversation_id = task["conversation_id"]
            model_name = task.get("model_name")

            # 执行任务并消费结果
            for _ in run_agent_query(
                user_query, user_info, access_token,
                conversation_id, model_name
            ):
                pass

            logger.info(f"后台任务完成 (Convo ID: {conversation_id})")

        except Exception as e:
            logger.error(f"Background task processing error: {e}")

    def submit_task(self, task):
        """提交后台任务"""
        self.task_queue.put(task)

# 全局后台任务管理器
BACKGROUND_MANAGER = BackgroundTaskManager()


def run_agent_query(
        user_query: str,
        user_info: dict = {},
        access_token: str = None,
        conversation_id: str = None,
        images: list = None,
        model_name: str = None,
) -> Generator:
    """
    优化后的查询处理函数
    """
    message_queue = queue.Queue()

    user_name = user_info.get("name")
    email = get_valid_user_email(user_info)
    union_id = user_info.get("union_id")
    summerfarm_api_token = user_info.get("summerfarm_api_token")

    user_obj = UserInfo(
        user_name=user_name,
        email=email,
        access_token=access_token,
        union_id=union_id,
        summerfarm_api_token=summerfarm_api_token,
        open_id=user_info.get("open_id", ""),
        conversation_id=conversation_id
    )

    max_retries = 3

    def async_worker():
        """优化后的异步工作函数 - 使用按需创建的事件循环"""
        loop = None
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 运行协程
            loop.run_until_complete(process_stream())

        except Exception as e:
            logger.exception(f"异步工作线程出错: {e}")
            message_queue.put({"type": "error", "content": str(e)})
            message_queue.put({"type": "final_result", "data": None})
        finally:
            # 确保事件循环被正确关闭
            if loop and not loop.is_closed():
                try:
                    # 取消所有待处理的任务
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()

                    # 等待任务取消完成
                    if pending:
                        loop.run_until_complete(
                            asyncio.gather(*pending, return_exceptions=True)
                        )

                    loop.close()
                    logger.debug("Event loop closed properly")
                except Exception as e:
                    logger.error(f"Error closing event loop: {e}")

            message_queue.put(None)  # 结束标记

    async def process_stream():
        """处理流事件的协程"""
        try:
            final_input_list = None

            # 获取历史
            history_for_agent = []
            if conversation_id:
                history_for_agent = get_conversation_history_as_input_list(
                    user_name, email, conversation_id
                )
                logger.info(f"获取到 {len(history_for_agent)} 条历史消息")

            # 构建用户消息，支持多模态
            if images and len(images) > 0:
                logger.info(f"开始处理 {len(images)} 张图片，下载并转换为base64编码")

                # 下载图片并转换为base64编码
                processed_images = process_images_for_ai(images)

                if processed_images:
                    # 使用正确的多模态消息格式
                    content_parts = [{"type": "input_text", "text": user_query}]

                    # 添加处理后的图片内容
                    for base64_image in processed_images:
                        content_parts.append({
                            "type": "input_image",
                            "image_url": base64_image,
                            "detail": "auto"
                        })

                    user_message = {
                        "role": "user",
                        "content": content_parts
                    }
                    logger.info(f"用户消息包含 {len(processed_images)} 张已处理的图片，使用多模态格式")
                else:
                    # 如果所有图片都处理失败，回退到纯文本消息
                    logger.warning("所有图片处理失败，回退到纯文本消息")
                    user_message = {"role": "user", "content": user_query}
            else:
                # 纯文本消息
                user_message = {"role": "user", "content": user_query}

            messages = history_for_agent + [user_message]

            for attempt in range(1, max_retries + 1):
                bot = CoordinatorBot(user_info)
                # 重置agent追踪，开始新的查询
                bot.tool_registry.reset_agent_tracking()
                agent = bot.create_agent(LITE_LLM_MODEL)

                total_messages = bot.get_user_realtime_instruction_as_message_object() + messages

                logger.info(f"第 {attempt} 次尝试处理查询")

                # 启动流式推理
                result = Runner.run_streamed(
                    agent,
                    input=total_messages,
                    max_turns=20,
                    run_config=RunConfig(
                        model_settings=ModelSettings(temperature=0.1)
                    ),
                    context=user_obj,
                )

                # 处理流事件
                collected_logs = ""
                tool_calls_detected = False
                agent_execution_logs = []
                used_agent_names = []  # 收集使用的agent名称

                try:
                    async for event in result.stream_events():
                        message = format_event_message(event)
                        if message:
                            msg_type = message.get("type")
                            content = message.get("content", "")

                            # 收集所有相关日志
                            if msg_type in ["log", "handoff_log", "tool_output"] and content:
                                collected_logs += str(content) + "\n"

                            # 检测工具调用（Agent as Tool架构的关键指标）
                            if msg_type == "tool_output" and content:
                                tool_calls_detected = True
                                # 尝试解析工具调用结果，提取Agent名称
                                agent_name = _extract_agent_name_from_tool_output(content)
                                if agent_name:
                                    # 收集使用的agent名称
                                    if agent_name not in used_agent_names:
                                        used_agent_names.append(agent_name)

                                    agent_log = f"🔄 CoordinatorBot调用专业工具: {agent_name}"
                                    agent_execution_logs.append(agent_log)
                                    logger.info(agent_log)
                                    # 发送类似handoff的日志消息，保持UI一致性
                                    message_queue.put({
                                        "type": "handoff_log",
                                        "content": agent_log
                                    })

                            message_queue.put(message)

                except Exception as e:
                    logger.exception(f"流事件处理出错: {e}")
                    if "Event loop is closed" in str(e):
                        message_queue.put({"type": "error", "content": "事件循环已关闭"})
                        break
                    raise

                # 检测执行成功（Agent as Tool架构）
                if tool_calls_detected or len(collected_logs.strip()) > 0:
                    # 有工具调用或有实质性输出，认为执行成功
                    success_msg = f"第 {attempt} 次尝试成功"
                    if agent_execution_logs:
                        success_msg += f"，调用了 {len(agent_execution_logs)} 个专业工具"
                    logger.info(success_msg)
                    final_input_list = result.to_input_list()
                    # 将bot对象和使用的agent名称传递给外部
                    message_queue.put({"type": "bot_instance", "data": bot})
                    message_queue.put({"type": "used_agents", "data": used_agent_names})
                    break
                else:
                    # 没有工具调用且没有实质性输出，可能需要重试
                    logger.warning(f"第 {attempt} 次尝试未检测到有效的工具调用或输出")
                    if attempt < max_retries:
                        message_queue.put({"type": "info", "content": "🤖未检测到有效响应，重试中..."})
                        continue
                    else:
                        logger.error(f"所有 {max_retries} 次尝试都未产生有效输出")
                        final_input_list = None
                        break

            message_queue.put({"type": "final_result", "data": final_input_list})

        except Exception as e:
            logger.exception(f"处理流时出错: {e}")
            message_queue.put({"type": "error", "content": str(e)})
            message_queue.put({"type": "final_result", "data": None})

    # 使用线程池执行异步工作
    future = THREAD_POOL.submit(async_worker)

    # 生成器函数
    def generate_and_save():
        full_assistant_response = ""
        full_assistant_logs = ""
        assistant_timestamp = None
        final_input_list_from_worker = None
        interrupted = False
        bot_instance = None  # 用于获取agent使用信息
        used_agents_list = []  # 收集使用的agent名称

        try:
            while True:
                try:
                    # 设置超时避免无限等待
                    message = message_queue.get(timeout=600)
                except queue.Empty:
                    logger.warning("消息队列获取超时")
                    # 通知用户超时情况
                    yield "[data]:" + json.dumps({"type": "error", "content": "响应超时，请稍后重试"}, ensure_ascii=False) + "\n"
                    break

                if message is None:
                    # 处理最终结果和保存逻辑
                    _handle_final_result(
                        final_input_list_from_worker,
                        full_assistant_response,
                        full_assistant_logs,
                        assistant_timestamp,
                        user_name, email, conversation_id,
                        used_agents_list
                    )
                    break

                if isinstance(message, dict):
                    msg_type = message.get("type")

                    if msg_type == "final_result":
                        final_input_list_from_worker = message.get("data")
                        continue

                    if msg_type == "bot_instance":
                        bot_instance = message.get("data")
                        continue

                    if msg_type == "used_agents":
                        used_agents_list = message.get("data", [])
                        continue

                    if not assistant_timestamp and msg_type != "error":
                        assistant_timestamp = int(datetime.now().timestamp() * 1000)

                    # 累积内容
                    if msg_type == "data":
                        full_assistant_response += message.get("content", "")
                    elif msg_type not in ["data", "final_result"] and message.get("content"):
                        log_line = f"[{msg_type.upper()}] {message.get('content', '')}"
                        full_assistant_logs += log_line + "\n"

                    # 发送到客户端
                    yield "[data]:" + json.dumps(message, ensure_ascii=False) + "\n"

        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            interrupted = True
            logger.warning(f"客户端断开连接: {e}")
        except Exception as e:
            logger.exception(f"生成器错误: {e}")
        finally:
            if interrupted:
                # 提交后台任务
                BACKGROUND_MANAGER.submit_task({
                    "user_query": user_query,
                    "user_info": user_info,
                    "access_token": access_token,
                    "conversation_id": conversation_id,
                    "user_timestamp": assistant_timestamp,
                    "model_name": model_name,
                })
                logger.info(f"任务已提交到后台队列 (Convo ID: {conversation_id})")

    def _handle_final_result(final_input_list, response, logs, timestamp,
                           user_name, email, conversation_id, used_agents_list=None):
        """处理最终结果和保存"""
        if not (response or logs):
            return

        try:
            structured_message = None
            if final_input_list and isinstance(final_input_list, list) and len(final_input_list) > 0:
                if (final_input_list and
                    isinstance(final_input_list[-1], dict) and
                    final_input_list[-1].get("role") == "assistant"):
                    structured_message = final_input_list[-1]

            output_as_input_json = None
            if structured_message:
                try:
                    output_as_input_json = json.dumps(
                        structured_message,
                        ensure_ascii=False,
                        indent=2
                    )
                except Exception as e:
                    logger.error(f"序列化结构化消息失败: {e}")

            if conversation_id and timestamp:
                # 获取使用的agent信息
                used_agents_str = None
                if used_agents_list and len(used_agents_list) > 0:
                    # 使用从工具调用中提取的agent名称
                    used_agents_str = ",".join(used_agents_list)
                    logger.debug(f"本次查询使用的agent: {used_agents_str}")
                elif bot_instance and hasattr(bot_instance, 'tool_registry'):
                    # 回退到从bot_instance获取
                    used_agents_str = bot_instance.tool_registry.get_used_agents_string()
                    logger.debug(f"从bot_instance获取的agent: {used_agents_str}")

                # 获取Tool Agent的详细执行日志
                enhanced_logs = logs if logs else ""
                if bot_instance and hasattr(bot_instance, 'tool_registry'):
                    try:
                        # 获取聚合的Tool Agent日志
                        tool_agent_logs = bot_instance.get_aggregated_tool_logs()
                        if tool_agent_logs and tool_agent_logs.strip():
                            # 将Tool Agent日志添加到现有日志中
                            if enhanced_logs:
                                enhanced_logs += "\n\n" + tool_agent_logs
                            else:
                                enhanced_logs = tool_agent_logs
                            logger.debug(f"已添加Tool Agent详细日志，总长度: {len(enhanced_logs)}")
                    except Exception as e:
                        logger.warning(f"获取Tool Agent日志时出错: {e}")

                save_assistant_message(
                    username=user_name,
                    email=email,
                    conversation_id=conversation_id,
                    content=response,
                    timestamp=timestamp,
                    logs=enhanced_logs if enhanced_logs else None,
                    output_as_input=output_as_input_json,
                    agent=used_agents_str
                )
                logger.debug(f"助手回复已保存 (Convo ID: {conversation_id})，包含Tool Agent日志")

        except Exception as e:
            logger.error(f"保存助手消息时出错: {e}")

    return generate_and_save()


def _extract_agent_name_from_tool_output(tool_output: str) -> str:
    """
    从工具输出中提取Agent名称

    Args:
        tool_output: 工具调用的输出内容

    Returns:
        str: 提取到的Agent名称，如果无法提取则返回"专业分析工具"
    """
    try:
        # 尝试解析JSON格式的工具输出
        if tool_output.strip().startswith('{'):
            import json
            data = json.loads(tool_output)
            agent_name = data.get("agent_name")
            if agent_name:
                return agent_name

        # 尝试从工具输出中匹配已知的Agent名称模式
        known_agents = [
            "sales_order_analytics",
            "warehouse_and_fulfillment",
            "general_chat_bot"
        ]

        tool_output_lower = tool_output.lower()
        for agent in known_agents:
            if agent in tool_output_lower:
                return agent

        # 如果包含"analysis"字样，说明是分析工具
        if "analysis" in tool_output_lower:
            return "专业分析工具"

        # 默认返回通用名称
        return "专业工具"

    except Exception as e:
        logger.debug(f"提取Agent名称时出错: {e}")
        return "专业工具"


# 优雅关闭函数
def shutdown_gracefully():
    """优雅关闭所有资源"""
    logger.info("开始优雅关闭...")

    # 记录关闭前的资源使用情况
    log_current_usage()

    # 关闭线程池
    THREAD_POOL.shutdown(wait=True)

    # 关闭后台任务管理器
    BACKGROUND_MANAGER.task_queue.put(None)
    BACKGROUND_MANAGER.executor.shutdown(wait=True)

    # 停止系统监控
    stop_system_monitoring()

    # 记录关闭后的资源使用情况
    log_current_usage()

    logger.info("优雅关闭完成")

# 启动系统监控
start_system_monitoring()

# 在应用退出时调用
import atexit
atexit.register(shutdown_gracefully)