"""
Agent工具化封装模块

将专业Agent封装为可被主Agent调用的工具函数，实现Agent as Tool架构。
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime

from agents import function_tool, Agent, Runner, RunConfig, ModelSettings
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.utils.logger import logger
from src.utils.resource_manager import list_resources


@dataclass
class AgentExecutionLog:
    """Agent执行过程的详细日志"""
    timestamp: str
    event_type: str  # 事件类型：tool_call, tool_output, log, error等
    content: str     # 日志内容
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外的元数据


@dataclass
class AgentToolResult:
    """Agent工具执行结果"""
    success: bool
    data: Any = None
    sql: Optional[str] = None
    error: Optional[str] = None
    agent_name: Optional[str] = None
    execution_time: Optional[float] = None
    # 新增：详细的执行日志
    execution_logs: List[AgentExecutionLog] = field(default_factory=list)
    # 新增：工具调用统计
    tool_calls_count: int = 0
    # 新增：使用的工具列表
    tools_used: List[str] = field(default_factory=list)
    # 新增：原始流事件数据（用于调试）
    raw_stream_events: List[Dict[str, Any]] = field(default_factory=list)


class AgentToolExecutor:
    """Agent工具执行器，提供统一的执行和错误处理"""

    def __init__(self, max_retries: int = 2, timeout: int = 600):
        self.max_retries = max_retries
        self.timeout = timeout
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "timeout_executions": 0,
            "avg_execution_time": 0.0
        }
        # 用于追踪当前查询中使用的所有agent
        self.used_agents = []
    
    async def execute_agent_query(
        self,
        agent: Agent,
        query: str,
        user_context: Any,
        agent_name: str = "unknown"
    ) -> AgentToolResult:
        """
        执行Agent查询，带重试和超时控制，并捕获详细的执行日志

        Args:
            agent: 要执行的Agent实例
            query: 用户查询
            user_context: 用户上下文
            agent_name: Agent名称，用于日志

        Returns:
            AgentToolResult: 执行结果，包含详细的执行日志
        """
        start_time = time.time()

        # 更新统计信息
        self.execution_stats["total_executions"] += 1

        for attempt in range(1, self.max_retries + 1):
            try:
                logger.info(f"🔧 执行{agent_name}工具，第{attempt}次尝试")

                # 使用流式执行来捕获详细日志
                result = await asyncio.wait_for(
                    self._execute_with_stream_logging(
                        agent, query, user_context, agent_name
                    ),
                    timeout=self.timeout
                )

                execution_time = time.time() - start_time

                logger.info(f"✅ {agent_name}工具执行成功，耗时{execution_time:.2f}秒")

                # 记录使用的agent
                if agent_name not in self.used_agents:
                    self.used_agents.append(agent_name)

                # 更新成功统计
                self.execution_stats["successful_executions"] += 1
                self._update_avg_execution_time(execution_time)

                return result
                
            except asyncio.TimeoutError:
                error_msg = f"{agent_name}工具执行超时（{self.timeout}秒）"
                logger.warning(f"⏰ {error_msg}，第{attempt}次尝试")
                if attempt == self.max_retries:
                    self.execution_stats["timeout_executions"] += 1
                    self.execution_stats["failed_executions"] += 1
                    return AgentToolResult(
                        success=False,
                        error=error_msg,
                        agent_name=agent_name,
                        execution_time=time.time() - start_time
                    )
                    
            except Exception as e:
                error_msg = f"{agent_name}工具执行出错: {str(e)}"
                logger.exception(f"❌ {error_msg}，第{attempt}次尝试")
                if attempt == self.max_retries:
                    self.execution_stats["failed_executions"] += 1
                    return AgentToolResult(
                        success=False,
                        error=error_msg,
                        agent_name=agent_name,
                        execution_time=time.time() - start_time
                    )
                    
                # 短暂等待后重试
                await asyncio.sleep(1)

    def get_used_agents(self) -> List[str]:
        """获取当前查询中使用的所有agent列表"""
        return self.used_agents.copy()

    def reset_used_agents(self):
        """重置使用的agent列表，用于新的查询"""
        self.used_agents = []

    def get_used_agents_string(self) -> str:
        """获取使用的agent列表的字符串表示，用于存储到数据库"""
        if not self.used_agents:
            return "coordinator_bot"

        # 确保coordinator_bot在列表开头
        agents = self.used_agents.copy()
        if "coordinator_bot" not in agents:
            agents.insert(0, "coordinator_bot")
        elif agents[0] != "coordinator_bot":
            agents.remove("coordinator_bot")
            agents.insert(0, "coordinator_bot")

        return ",".join(agents)
    
    def _update_avg_execution_time(self, execution_time: float):
        """更新平均执行时间"""
        current_avg = self.execution_stats["avg_execution_time"]
        successful_count = self.execution_stats["successful_executions"]

        if successful_count == 1:
            self.execution_stats["avg_execution_time"] = execution_time
        else:
            # 计算新的平均值
            self.execution_stats["avg_execution_time"] = (
                (current_avg * (successful_count - 1) + execution_time) / successful_count
            )

    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return self.execution_stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "timeout_executions": 0,
            "avg_execution_time": 0.0
        }

    async def _execute_with_stream_logging(
        self,
        agent: Agent,
        query: str,
        user_context: Any,
        agent_name: str
    ) -> AgentToolResult:
        """
        使用流式执行并捕获详细的执行日志

        Args:
            agent: Agent实例
            query: 查询内容
            user_context: 用户上下文
            agent_name: Agent名称

        Returns:
            AgentToolResult: 包含详细日志的执行结果
        """
        from agents import Runner, RunConfig, ModelSettings
        from src.services.agent.utils.formatter import format_event_message

        execution_logs = []
        raw_stream_events = []
        tools_used = []
        tool_calls_count = 0

        try:
            # 使用流式执行
            result = Runner.run_streamed(
                agent,
                input=query,
                max_turns=10,
                run_config=RunConfig(
                    model_settings=ModelSettings(temperature=0.1)
                ),
                context=user_context,
            )

            # 处理流事件并收集日志
            async for event in result.stream_events():
                # 记录原始事件（用于调试）
                raw_stream_events.append({
                    "type": event.type,
                    "timestamp": datetime.now().isoformat(),
                    "data": str(event)[:500]  # 限制长度避免过大
                })

                # 格式化事件消息
                formatted_message = format_event_message(event)
                if formatted_message:
                    msg_type = formatted_message.get("type", "unknown")
                    content = formatted_message.get("content", "")

                    # 创建执行日志条目
                    log_entry = AgentExecutionLog(
                        timestamp=datetime.now().isoformat(),
                        event_type=msg_type,
                        content=content,
                        metadata={
                            "agent_name": agent_name,
                            "event_raw_type": event.type
                        }
                    )
                    execution_logs.append(log_entry)

                    # 统计工具调用
                    if msg_type == "tool_call":
                        tool_calls_count += 1
                        # 尝试提取工具名称
                        if "工具:" in content:
                            tool_name = content.split("工具:")[1].split()[0] if "工具:" in content else "unknown"
                            if tool_name not in tools_used:
                                tools_used.append(tool_name)

                    # 记录重要的日志信息
                    if msg_type in ["tool_call", "tool_output", "error"]:
                        logger.debug(f"[{agent_name}] {msg_type}: {content[:100]}...")

            # 获取最终结果
            final_output = result.final_output if hasattr(result, 'final_output') else str(result)
            sql = self._extract_sql_from_result(result)

            return AgentToolResult(
                success=True,
                data=final_output,
                sql=sql,
                agent_name=agent_name,
                execution_time=0,  # 将在上层计算
                execution_logs=execution_logs,
                tool_calls_count=tool_calls_count,
                tools_used=tools_used,
                raw_stream_events=raw_stream_events
            )

        except Exception as e:
            # 记录错误日志
            error_log = AgentExecutionLog(
                timestamp=datetime.now().isoformat(),
                event_type="error",
                content=f"执行出错: {str(e)}",
                metadata={"agent_name": agent_name}
            )
            execution_logs.append(error_log)

            return AgentToolResult(
                success=False,
                error=str(e),
                agent_name=agent_name,
                execution_time=0,
                execution_logs=execution_logs,
                tool_calls_count=tool_calls_count,
                tools_used=tools_used,
                raw_stream_events=raw_stream_events
            )

    def _extract_sql_from_result(self, result) -> Optional[str]:
        """从Agent执行结果中提取SQL语句"""
        try:
            # 这里可以根据实际的结果格式来提取SQL
            # 暂时返回None，后续可以根据需要实现
            return None
        except Exception:
            return None


class AgentToolRegistry:
    """Agent工具注册中心"""
    
    def __init__(self, user_info: Dict[str, Any]):
        self.user_info = user_info
        self.executor = AgentToolExecutor()
        self.registered_tools = {}
        # 新增：保存详细的执行结果
        self.execution_results: List[AgentToolResult] = []

    def get_used_agents_string(self) -> str:
        """获取本次查询使用的所有agent的字符串表示"""
        return self.executor.get_used_agents_string()

    def reset_agent_tracking(self):
        """重置agent追踪，用于新的查询"""
        self.executor.reset_used_agents()
        # 清空之前的执行结果
        self.execution_results.clear()

    def add_execution_result(self, result: AgentToolResult):
        """添加执行结果到记录中"""
        self.execution_results.append(result)

    def get_all_execution_logs(self) -> str:
        """
        获取所有Tool Agent的聚合执行日志

        Returns:
            str: 格式化的聚合日志字符串
        """
        if not self.execution_results:
            return "无Tool Agent执行记录"

        log_lines = []
        log_lines.append("=== Tool Agent 详细执行日志 ===")

        for i, result in enumerate(self.execution_results, 1):
            log_lines.append(f"\n--- Agent {i}: {result.agent_name} ---")
            log_lines.append(f"执行状态: {'成功' if result.success else '失败'}")
            log_lines.append(f"执行时间: {result.execution_time:.2f}秒")

            if result.tools_used:
                log_lines.append(f"使用的工具: {', '.join(result.tools_used)}")

            log_lines.append(f"工具调用次数: {result.tool_calls_count}")

            if result.error:
                log_lines.append(f"错误信息: {result.error}")

            # 添加详细的执行日志
            if result.execution_logs:
                log_lines.append("执行过程:")
                for log_entry in result.execution_logs:
                    log_lines.append(f"  [{log_entry.timestamp}] {log_entry.event_type}: {log_entry.content[:500]}...")

        log_lines.append("\n=== Tool Agent 日志结束 ===")
        return "\n".join(log_lines)

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要统计"""
        if not self.execution_results:
            return {"total": 0, "successful": 0, "failed": 0, "agents": []}

        successful = sum(1 for r in self.execution_results if r.success)
        failed = len(self.execution_results) - successful
        agents = [r.agent_name for r in self.execution_results]
        total_time = sum(r.execution_time for r in self.execution_results if r.execution_time)

        return {
            "total": len(self.execution_results),
            "successful": successful,
            "failed": failed,
            "agents": agents,
            "total_execution_time": total_time,
            "avg_execution_time": total_time / len(self.execution_results) if self.execution_results else 0
        }
    
    def register_data_fetcher_tools(self) -> List[Callable]:
        """注册所有数据获取Agent为工具"""
        tools = []
        
        # 获取所有数据获取Agent配置
        data_fetcher_configs = list_resources('data_fetcher_bot_config', '.yml')
        
        for config_file in data_fetcher_configs:
            try:
                # 创建DataFetcherBot实例
                bot = DataFetcherBot(self.user_info, config_file)
                agent_config = bot.config
                
                # 创建工具函数
                tool_func = self._create_agent_tool(bot, agent_config)
                tools.append(tool_func)
                
                # 注册到工具字典
                agent_name = agent_config.get('agent_name', config_file)
                self.registered_tools[agent_name] = tool_func
                
                logger.info(f"📝 注册Agent工具: {agent_name}")
                
            except Exception as e:
                logger.exception(f"注册Agent工具失败，配置文件: {config_file}, 错误: {e}")
        
        return tools
    
    def _create_agent_tool(self, bot: BaseBot, config: Dict[str, Any]) -> Callable:
        """为单个Bot创建工具函数"""
        agent_name = config.get('agent_name', 'unknown_agent')
        agent_description = config.get('agent_description', '未提供描述')
        
        # 构建工具描述
        tool_description = f"专业分析工具：{agent_name}。{agent_description}"
        
        @function_tool(
            name_override=f"{agent_name}_analysis",
            description_override=tool_description
        )
        async def agent_tool(query: str) -> str:
            """
            执行专业Agent分析
            
            Args:
                query: 用户的具体查询问题
                
            Returns:
                str: 分析结果的JSON字符串
            """
            try:
                # 创建Agent实例
                agent = bot.create_agent()
                
                # 创建用户上下文对象
                from src.services.feishu.user_service import UserService
                user_context = UserService.create_user_info_object(self.user_info)
                
                # 执行查询
                result = await self.executor.execute_agent_query(
                    agent=agent,
                    query=query,
                    user_context=user_context,
                    agent_name=agent_name
                )

                # 保存执行结果到注册中心（包含详细日志）
                self.add_execution_result(result)

                # 返回给Coordinator的简洁数据（不包含详细日志，节省token）
                response_data = {
                    "success": result.success,
                    "agent_name": result.agent_name,  # 放在前面便于检测
                    "data": result.data,  # 只包含核心业务数据
                    "sql": result.sql,
                    "error": result.error,
                    "execution_time": result.execution_time,
                    # 只包含简单的统计信息，不包含详细日志内容
                    "tool_calls_count": result.tool_calls_count,
                    "tools_used_count": len(result.tools_used)
                }

                # 记录工具执行日志，便于runner检测（详细日志已保存到注册中心）
                logger.info(f"🔧 专业工具 {result.agent_name} 执行完成，耗时 {result.execution_time:.2f}秒，工具调用{result.tool_calls_count}次，日志{len(result.execution_logs)}条")

                # 返回简洁的JSON数据给Coordinator（不包含详细日志，节省token）
                return json.dumps(response_data, ensure_ascii=False, indent=2)
                
            except Exception as e:
                logger.exception(f"Agent工具执行异常: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"工具执行异常: {str(e)}",
                    "agent_name": agent_name
                }, ensure_ascii=False, indent=2)
        
        return agent_tool
    
    def get_all_tools(self) -> List[Callable]:
        """获取所有注册的工具"""
        return list(self.registered_tools.values())
    
    def get_tool_by_name(self, name: str) -> Optional[Callable]:
        """根据名称获取工具"""
        return self.registered_tools.get(name)

    async def _execute_agent_directly(self, agent_name: str, query: str) -> Optional[str]:
        """
        直接执行Agent，用于并行分析工具

        Args:
            agent_name: Agent名称
            query: 查询内容

        Returns:
            str: 执行结果的JSON字符串，如果失败返回None
        """
        try:
            # 查找对应的Bot配置
            data_fetcher_configs = list_resources('data_fetcher_bot_config', '.yml')

            for config_file in data_fetcher_configs:
                try:
                    bot = DataFetcherBot(self.user_info, config_file)
                    if bot.config.get('agent_name') == agent_name:
                        # 找到对应的Bot，执行查询
                        agent = bot.create_agent()

                        # 创建用户上下文对象
                        from src.services.feishu.user_service import UserService
                        user_context = UserService.create_user_info_object(self.user_info)

                        # 执行查询
                        result = await self.executor.execute_agent_query(
                            agent=agent,
                            query=query,
                            user_context=user_context,
                            agent_name=agent_name
                        )

                        # 返回JSON格式的结果
                        response_data = {
                            "success": result.success,
                            "agent_name": result.agent_name,
                            "data": result.data,
                            "sql": result.sql,
                            "error": result.error,
                            "execution_time": result.execution_time
                        }

                        return json.dumps(response_data, ensure_ascii=False, indent=2)

                except Exception as e:
                    logger.exception(f"执行Agent {agent_name} 时出错: {e}")
                    continue

            # 如果没有找到对应的Agent
            logger.warning(f"未找到Agent: {agent_name}")
            return json.dumps({
                "success": False,
                "error": f"未找到Agent: {agent_name}",
                "agent_name": agent_name
            }, ensure_ascii=False)

        except Exception as e:
            logger.exception(f"直接执行Agent {agent_name} 时出错: {e}")
            return json.dumps({
                "success": False,
                "error": f"执行Agent时出错: {str(e)}",
                "agent_name": agent_name
            }, ensure_ascii=False)


# 并行执行工具
def create_parallel_analysis_tool(registry: AgentToolRegistry) -> Callable:
    """创建并行分析工具"""

    @function_tool(
        name_override="run_parallel_analysis",
        description_override="并行运行多个专业分析，适用于需要多个领域协作的复杂查询"
    )
    async def parallel_analysis_tool(analysis_requests: str) -> str:
        """
        并行执行多个专业分析

        Args:
            analysis_requests: JSON格式的分析请求列表，格式：
                [{"agent_name": "sales_order_analytics", "query": "查询销售数据"}]

        Returns:
            str: 所有分析结果的JSON字符串
        """
        try:
            # 解析请求
            requests = json.loads(analysis_requests)
            if not isinstance(requests, list):
                return json.dumps({
                    "success": False,
                    "error": "analysis_requests必须是JSON数组格式"
                }, ensure_ascii=False)

            # 准备并行任务
            tasks = []
            for req in requests:
                agent_name = req.get('agent_name')
                query = req.get('query')

                if not agent_name or not query:
                    continue

                # 直接使用registry的执行器来执行Agent
                # 而不是尝试调用FunctionTool对象
                task = registry._execute_agent_directly(agent_name, query)
                if task:
                    tasks.append(task)

            if not tasks:
                return json.dumps({
                    "success": False,
                    "error": "没有找到有效的分析请求"
                }, ensure_ascii=False)

            # 并行执行
            logger.info(f"🚀 开始并行执行{len(tasks)}个分析任务")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        "success": False,
                        "error": str(result),
                        "request_index": i
                    })
                else:
                    # 尝试解析JSON结果
                    try:
                        parsed_result = json.loads(result) if isinstance(result, str) else result
                        parsed_result["request_index"] = i
                        processed_results.append(parsed_result)
                    except:
                        processed_results.append({
                            "success": False,
                            "error": "结果解析失败",
                            "raw_result": str(result),
                            "request_index": i
                        })

            return json.dumps({
                "success": True,
                "results": processed_results,
                "total_tasks": len(tasks)
            }, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.exception(f"并行分析工具执行异常: {e}")
            return json.dumps({
                "success": False,
                "error": f"并行分析执行异常: {str(e)}"
            }, ensure_ascii=False)

    return parallel_analysis_tool
